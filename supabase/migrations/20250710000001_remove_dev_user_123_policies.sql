/*
  # 移除dev-user-123硬编码测试用户策略 - 2025-07-10
  
  1. 问题
    - 数据库中存在大量dev-user-123硬编码策略
    - 开发和生产环境认证逻辑不一致
    - 安全隐患：硬编码测试用户可能泄露到生产环境
    
  2. 解决方案
    - 删除所有dev-user-123相关的RLS策略
    - 移除测试用户数据
    - 统一使用标准的Supabase认证策略
    - 简化认证逻辑，提升安全性
    
  3. 影响范围
    - users表：删除dev-user-123特殊策略和测试数据
    - credit_transactions表：删除dev-user-123访问策略
    - readings表：删除dev-user-123访问策略
    - 辅助函数：移除dev-user-123相关逻辑
*/

-- 删除users表的dev-user-123策略
DROP POLICY IF EXISTS "Dev user can access all users" ON users;

-- 删除credit_transactions表的dev-user-123策略
DROP POLICY IF EXISTS "Dev user can access all credit transactions" ON credit_transactions;

-- 删除readings表的dev-user-123策略
DROP POLICY IF EXISTS "Dev user can access all readings" ON readings;

-- 删除credits表的dev-user-123策略（如果存在）
DROP POLICY IF EXISTS "Dev user can access all credits" ON credits;

-- 移除测试用户数据
DELETE FROM users WHERE clerk_user_id = 'dev-user-123';

-- 更新辅助函数，移除dev-user-123逻辑
CREATE OR REPLACE FUNCTION user_can_access_record(record_user_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN (
    record_user_id = get_current_user_id() OR
    auth.role() = 'service_role'
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 验证所有表的RLS策略都已正确配置（无dev-user-123引用）

-- 确认users表策略
DO $$
BEGIN
  -- 检查是否还有dev-user-123相关策略
  IF EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE tablename = 'users' 
    AND definition LIKE '%dev-user-123%'
  ) THEN
    RAISE EXCEPTION 'users表仍存在dev-user-123相关策略';
  END IF;
  
  RAISE NOTICE '✅ users表已清理完成，无dev-user-123策略';
END $$;

-- 确认credit_transactions表策略
DO $$
BEGIN
  IF EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE tablename = 'credit_transactions' 
    AND definition LIKE '%dev-user-123%'
  ) THEN
    RAISE EXCEPTION 'credit_transactions表仍存在dev-user-123相关策略';
  END IF;
  
  RAISE NOTICE '✅ credit_transactions表已清理完成，无dev-user-123策略';
END $$;

-- 确认readings表策略
DO $$
BEGIN
  IF EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE tablename = 'readings' 
    AND definition LIKE '%dev-user-123%'
  ) THEN
    RAISE EXCEPTION 'readings表仍存在dev-user-123相关策略';
  END IF;
  
  RAISE NOTICE '✅ readings表已清理完成，无dev-user-123策略';
END $$;

-- 确认测试用户数据已删除
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM users WHERE clerk_user_id = 'dev-user-123') THEN
    RAISE EXCEPTION '测试用户数据仍然存在';
  END IF;
  
  RAISE NOTICE '✅ 测试用户数据已删除';
END $$;

-- 显示当前所有表的RLS策略状态
DO $$
DECLARE
  policy_record RECORD;
BEGIN
  RAISE NOTICE '=== 当前RLS策略状态 ===';
  
  FOR policy_record IN 
    SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual, with_check
    FROM pg_policies 
    WHERE schemaname = 'public'
    ORDER BY tablename, policyname
  LOOP
    RAISE NOTICE '表: % | 策略: % | 命令: %', 
      policy_record.tablename, 
      policy_record.policyname, 
      policy_record.cmd;
  END LOOP;
END $$;

-- 添加注释说明
COMMENT ON FUNCTION user_can_access_record(UUID) IS '验证用户访问权限的辅助函数，已移除dev-user-123特殊逻辑';

-- 记录迁移完成
INSERT INTO app_configs (key, value, description, is_secret) 
VALUES (
  'dev_user_123_removed', 
  'true', 
  '标记dev-user-123硬编码用户已被移除 - 2025-07-10', 
  false
) ON CONFLICT (key) DO UPDATE SET 
  value = EXCLUDED.value,
  description = EXCLUDED.description,
  updated_at = timezone('utc'::text, now());

RAISE NOTICE '🎉 dev-user-123清理完成！数据库现在使用统一的认证策略。';
