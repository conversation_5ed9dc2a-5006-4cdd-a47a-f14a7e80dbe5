import { supabase } from './supabase';

export interface CreditTransaction {
  id: string;
  user_id: string;
  type: 'purchase' | 'consume' | 'refund' | 'bonus' | 'admin_adjust' | 'expire';
  amount: number;
  balance_before: number;
  balance_after: number;
  reading_id?: string;
  payment_id?: string;
  package_id?: string;
  description?: string;
  reference_id?: string;
  status: 'pending' | 'completed' | 'failed' | 'reversed';
  expires_at?: string;
  is_expired: boolean;
  created_at: string;
  updated_at: string;
}

export interface CreditPackage {
  id: string;
  name: string;
  description?: string;
  credits: number;
  price_usd: number;
  price_cny?: number;
  currency: string;
  bonus_credits: number;
  validity_days?: number;
  package_type: 'standard' | 'premium' | 'promotional';
  display_order: number;
  is_popular: boolean;
  is_featured: boolean;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

class CreditService {
  // 获取当前用户的认证token
  private async getAuthToken(): Promise<string> {
    const { supabase } = await import('./supabase');
    const { data: { session } } = await supabase.auth.getSession();
    return session?.access_token || '';
  }
  async getUserCredits(userId: string): Promise<{ credits: number; error: string | null }> {
    try {
      // 统一使用真实的认证token
      const authToken = await this.getAuthToken();

      // 调用后端 API 获取积分余额
      const response = await fetch('http://localhost:8787/v1/credits', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${authToken}`
        }
      });

      const result = await response.json();

      if (!response.ok || !result.success) {
        return {
          credits: 0,
          error: result.error?.message || 'Failed to get credits'
        };
      }

      return {
        credits: result.data.balance || 0,
        error: null
      };
    } catch (error: any) {
      return { credits: 0, error: error.message };
    }
  }

  async consumeCredits(
    userId: string,
    amount: number,
    type: string = 'reading',
    referenceId?: string,
    description?: string
  ): Promise<{ success: boolean; transaction?: CreditTransaction; error?: string }> {
    try {
      // 统一使用真实的认证token
      const authToken = await this.getAuthToken();

      // 调用后端 API 进行积分消费
      const response = await fetch('http://localhost:8788/v1/credits/consume', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authToken}`
        },
        body: JSON.stringify({
          amount,
          description: description || `${type} consumption`
        })
      });

      const result = await response.json();

      if (!response.ok || !result.success) {
        return {
          success: false,
          error: result.error?.message || 'Failed to consume credits'
        };
      }

      return {
        success: true,
        transaction: {
          id: Date.now().toString(), // 临时 ID
          user_id: userId,
          type: 'consume',
          amount: -amount,
          description: description || `${type} consumption`,
          created_at: new Date().toISOString(),
          balance_before: result.data.previousBalance,
          balance_after: result.data.newBalance,
          status: 'completed'
        } as CreditTransaction
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  async addCredits(
    userId: string,
    amount: number,
    type: 'purchase' | 'bonus' | 'admin_adjust' = 'purchase',
    referenceId?: string,
    description?: string
  ): Promise<{ success: boolean; transaction?: CreditTransaction; error?: string }> {
    try {
      // 统一使用真实的认证token
      const authToken = await this.getAuthToken();

      // Add credits via API
      const response = await fetch('http://localhost:8788/v1/credits/add', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authToken}`
        },
        body: JSON.stringify({
          amount,
          type: type === 'admin_adjust' ? 'admin_adjustment' : type,
          description: description || `${type} addition`,
          reference_id: referenceId
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error?.message || 'Failed to add credits');
      }

      const { data } = await response.json();
      return {
        success: true,
        transaction: data.transaction
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  async getCreditTransactions(
    userId: string,
    page: number = 1,
    limit: number = 20,
    type?: string
  ): Promise<{
    transactions: CreditTransaction[];
    total: number;
    error: string | null
  }> {
    try {
      // Get transactions via API
      const params = new URLSearchParams({
        limit: limit.toString()
      });

      if (type && type !== 'all') {
        params.append('type', type);
      }

      // 统一使用真实的认证token
      const authToken = await this.getAuthToken();

      const response = await fetch(`http://localhost:8788/v1/credits/transactions?${params}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${authToken}`
        }
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error?.message || 'Failed to get transactions');
      }

      const { data } = await response.json();
      return {
        transactions: data.transactions || [],
        total: data.transactions?.length || 0,
        error: null
      };
    } catch (error: any) {
      return {
        transactions: [],
        total: 0,
        error: error.message
      };
    }
  }

  async getCreditPackages(): Promise<{ packages: CreditPackage[]; error: string | null }> {
    try {
      // Get packages via API
      const response = await fetch('http://localhost:8787/v1/credits/packages', {
        method: 'GET'
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error?.message || 'Failed to get credit packages');
      }

      const { data } = await response.json();
      return {
        packages: data.packages || [],
        error: null
      };
    } catch (error: any) {
      return {
        packages: [],
        error: error.message
      };
    }
  }

  async createInvitation(userId: string): Promise<{ 
    invitationCode: string; 
    invitationUrl: string; 
    error: string | null 
  }> {
    try {
      // Generate unique invitation code
      const invitationCode = `INVITE${Date.now()}${Math.random().toString(36).substr(2, 5).toUpperCase()}`;
      
      // Store invitation in database (you might want to create an invitations table)
      // For now, we'll return the code directly
      
      const invitationUrl = `${window.location.origin}/register?invite=${invitationCode}`;
      
      return {
        invitationCode,
        invitationUrl,
        error: null
      };
    } catch (error: any) {
      return {
        invitationCode: '',
        invitationUrl: '',
        error: error.message
      };
    }
  }
}

export const creditService = new CreditService();