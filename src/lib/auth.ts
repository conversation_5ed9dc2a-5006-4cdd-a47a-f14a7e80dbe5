import { supabase } from './supabase';
import { User } from '@supabase/supabase-js';

export interface AuthUser {
  id: string;
  email: string;
  name?: string;
  avatar_url?: string;
  credits: number;
  language_preference: string;
  subscription_status: string;
  total_readings: number;
}

export interface AuthState {
  user: AuthUser | null;
  loading: boolean;
  error: string | null;
}

export type AuthProvider = 'google' | 'github' | 'discord' | 'facebook' | 'twitter';

class AuthService {
  async signUp(email: string, password: string, name?: string) {
    try {
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            name: name || '',
          }
        }
      });

      if (error) throw error;

      // Create user profile via API
      if (data.user) {
        try {
          const response = await fetch('http://localhost:8787/v1/users/register', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              clerk_user_id: data.user.id,
              email: data.user.email!,
              name: name || '',
              credits: 1, // Welcome credit
              language_preference: 'zh'
            })
          });

          if (!response.ok) {
            const errorData = await response.json();
            console.error('Profile creation error:', errorData);
          }
        } catch (apiError) {
          console.error('Failed to create user profile via API:', apiError);
        }
      }

      return { data, error: null };
    } catch (error: any) {
      return { data: null, error: error.message };
    }
  }

  async signIn(email: string, password: string) {
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password
      });

      if (error) throw error;
      return { data, error: null };
    } catch (error: any) {
      return { data: null, error: error.message };
    }
  }

  async signInWithProvider(provider: AuthProvider) {
    try {
      const { data, error } = await supabase.auth.signInWithOAuth({
        provider,
        options: {
          redirectTo: `${window.location.origin}/auth/callback`
        }
      });

      if (error) throw error;
      return { data, error: null };
    } catch (error: any) {
      return { data: null, error: error.message };
    }
  }

  async signOut() {
    try {
      const { error } = await supabase.auth.signOut();
      if (error) throw error;
      return { error: null };
    } catch (error: any) {
      return { error: error.message };
    }
  }

  async getCurrentUser(): Promise<AuthUser | null> {
    try {
      const { data: { user, session } } = await supabase.auth.getUser();

      // 如果没有Supabase session，返回null
      if (!user || !session) {
        return null;
      }

      // 使用真实的Supabase session进行认证
      console.log('✅ Found Supabase session, using real user authentication');
      const authToken = session.access_token;

      // Get user profile via API using the session token
      const response = await fetch('http://localhost:8787/v1/users/profile', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${authToken}`
        }
      });

      if (!response.ok) {
        console.error('Failed to get user profile via API:', response.status);
        // 如果API调用失败，返回基本用户信息
        return {
          id: user.id,
          email: user.email || '',
          name: user.user_metadata?.name || '',
          avatar_url: user.user_metadata?.avatar_url,
          credits: 0,
          language_preference: 'zh',
          subscription_status: 'free',
          total_readings: 0
        };
      }

      const { data: profile } = await response.json();
      if (!profile) return null;

      return {
        id: profile.id,
        email: profile.email,
        name: profile.name,
        avatar_url: profile.avatar_url,
        credits: profile.credits,
        language_preference: profile.language_preference,
        subscription_status: profile.subscription_status,
        total_readings: profile.total_readings
      };
    } catch (error) {
      console.error('Get current user error:', error);
      return null;
    }
  }

  async updateProfile(updates: Partial<AuthUser>) {
    try {
      const { data: { user, session } } = await supabase.auth.getUser();
      if (!user || !session) throw new Error('No authenticated user');

      // Update profile via API
      const response = await fetch('http://localhost:8787/v1/users/profile', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.access_token}`
        },
        body: JSON.stringify(updates)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error?.message || 'Failed to update profile');
      }

      const { data } = await response.json();
      return { data, error: null };
    } catch (error: any) {
      return { data: null, error: error.message };
    }
  }

  onAuthStateChange(callback: (user: User | null) => void) {
    return supabase.auth.onAuthStateChange((event, session) => {
      callback(session?.user || null);
    });
  }
}

export const authService = new AuthService();