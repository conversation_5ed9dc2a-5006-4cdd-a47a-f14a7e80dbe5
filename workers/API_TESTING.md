# Eastern Fate Master API 测试指南

## 🌐 API 基础信息

**本地开发地址**: http://localhost:8787
**API 版本**: v1
**认证方式**: Bearer Token

## 🔑 认证方式

使用真实的 Supabase JWT Token 进行认证：
```
Authorization: Bearer <your_supabase_jwt_token>
```

### 获取认证Token
1. 在前端应用中注册/登录用户
2. 从浏览器开发者工具中获取 JWT token
3. 或使用 Supabase 客户端获取 session.access_token

## 📋 API 端点列表

### 基础端点

#### 1. 根路径
```http
GET /
```
返回 API 基本信息和可用端点列表。

#### 2. 健康检查
```http
GET /health
```
返回服务健康状态和各组件状态。

#### 3. 测试端点
```http
GET /v1/test
```
简单的测试端点，验证 API 是否正常工作。

### 用户管理 API

#### 1. 获取用户信息
```http
GET /v1/users/profile
Authorization: Bearer <your_jwt_token>
```

#### 2. 获取用户统计
```http
GET /v1/users/stats
Authorization: Bearer <your_jwt_token>
```

### 积分系统 API

#### 1. 获取积分余额
```http
GET /v1/credits
Authorization: Bearer <your_jwt_token>
```

#### 2. 获取积分套餐
```http
GET /v1/credits/packages
```

#### 3. 获取积分配置
```http
GET /v1/credits/config
```

#### 4. 获取积分交易历史
```http
GET /v1/credits/transactions
Authorization: Bearer <your_jwt_token>
```

### 八字分析 API

#### 1. 创建八字分析
```http
POST /v1/readings
Authorization: Bearer <your_jwt_token>
Content-Type: application/json

{
  "birthInfo": {
    "date": "1990-05-15",
    "time": "14:30",
    "location": "北京市",
    "timezone": "Asia/Shanghai",
    "gender": "male"
  }
}
```

#### 2. 获取分析结果
```http
GET /v1/readings/{reading_id}
Authorization: Bearer <your_jwt_token>
```

#### 3. 获取分析历史
```http
GET /v1/readings
Authorization: Bearer <your_jwt_token>
```

## 🧪 测试用例

### 测试用例 1: 完整的八字分析流程

1. **检查用户积分**
```bash
curl -X GET "http://localhost:8787/v1/credits" \
  -H "Authorization: Bearer <your_jwt_token>"
```

2. **创建八字分析**
```bash
curl -X POST "http://localhost:8787/v1/readings" \
  -H "Authorization: Bearer <your_jwt_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "birthInfo": {
      "date": "1990-05-15",
      "time": "14:30",
      "location": "北京市",
      "timezone": "Asia/Shanghai",
      "gender": "male"
    }
  }'
```

3. **检查积分消费**
```bash
curl -X GET "http://localhost:8787/v1/credits/transactions" \
  -H "Authorization: Bearer <your_jwt_token>"
```

### 测试用例 2: 错误处理测试

1. **无认证访问**
```bash
curl -X GET "http://localhost:8787/v1/users/profile"
```
预期: 401 Unauthorized

2. **无效输入数据**
```bash
curl -X POST "http://localhost:8787/v1/readings" \
  -H "Authorization: Bearer <your_jwt_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "birthInfo": {
      "date": "invalid-date",
      "time": "25:70"
    }
  }'
```
预期: 400 Bad Request

## 📊 预期响应格式

### 成功响应
```json
{
  "success": true,
  "data": {
    // 响应数据
  },
  "timestamp": "2025-01-09T13:00:00.000Z",
  "requestId": "req_1234567890_abcdef"
}
```

### 错误响应
```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "错误描述",
    "details": {}
  },
  "timestamp": "2025-01-09T13:00:00.000Z",
  "requestId": "req_1234567890_abcdef"
}
```

## 🔍 调试信息

### 日志格式
每个请求都会在控制台输出日志：
```
[req_1234567890_abcdef] GET /v1/test - ::1
```

### 错误追踪
所有错误都包含 requestId，便于追踪和调试。

## 📝 注意事项

1. **认证要求**: 需要真实的 Supabase JWT token 进行认证
2. **用户注册**: 需要先在前端应用中注册用户账号
3. **数据库连接**: 某些功能需要正确的 Supabase 配置
4. **AI 服务**: DeepSeek API 需要有效的 API 密钥
5. **积分系统**: 新用户默认有 1 积分，需要购买积分套餐
6. **缓存**: 配置信息会缓存 5 分钟

## 🚀 下一步测试

完成基础 API 测试后，可以进行：
1. 前端集成测试
2. 性能压力测试
3. 错误恢复测试
4. 安全性测试
